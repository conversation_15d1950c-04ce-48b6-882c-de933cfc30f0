export enum OrderStatus {
  待付款 = '待付款',
  待接单 = '待接单',
  待服务 = '待服务',
  已出发 = '已出发',
  服务中 = '服务中',
  已完成 = '已完成',
  已评价 = '已评价',
  已取消 = '已取消',
  退款中 = '退款中',
  已退款 = '已退款',
}

export enum OrderStatusChangeType {
  下单 = '下单',
  付款 = '付款',
  接单 = '接单',
  派单 = '派单',
  转单 = '转单',
  修改服务时间 = '修改服务时间',
  出发 = '出发',
  开始服务 = '开始服务',
  完成订单 = '完成订单',
  取消订单 = '取消订单',
  申请退款 = '申请退款',
  退款 = '退款',
}

/** 适用范围 */
export enum ApplicableScope {
  不限 = 'all',
  所有服务 = 'allServices',
  指定服务类别 = 'serviceType',
  指定服务品牌 = 'serviceCategory',
  指定服务 = 'service',
  所有商品 = 'allProducts',
  指定商品类别 = 'productCategory',
  指定商品 = 'product',
}

/**
 * 消息类型枚举
 */
export enum MessageType {
  SYSTEM = 'system',
  PLATFORM = 'platform',
  ORDER = 'order',
}

/**
 * 推送状态枚举
 */
export enum PushStatus {
  PENDING = 'pending',
  SUCCESS = 'success',
  FAILED = 'failed',
}

/**
 * 推送类型枚举
 */
export enum PushType {
  WECHAT = 'wechat',
  SMS = 'sms',
  EMAIL = 'email',
}

/**
 * 消息系统常量配置
 */
export const MESSAGE_CONSTANTS = {
  // 消息类型配置
  messageTypes: {
    SYSTEM: 'system' as const,
    PLATFORM: 'platform' as const,
    ORDER: 'order' as const,
  },

  // 消息模板代码
  templateCodes: {
    ORDER_NEW: 'ORDER_NEW',
    ORDER_ACCEPTED: 'ORDER_ACCEPTED',
    ORDER_COMPLETED: 'ORDER_COMPLETED',
    SYSTEM_MAINTENANCE: 'SYSTEM_MAINTENANCE',
    PLATFORM_POLICY: 'PLATFORM_POLICY',
  },

  // 分页配置
  pagination: {
    defaultPage: 1,
    defaultLimit: 20,
    maxLimit: 100,
  },

  // 消息保留时间（天）
  retentionDays: {
    system: 30, // 系统消息保留30天
    platform: 60, // 平台消息保留60天
    order: 90, // 订单消息保留90天
  },

  // 推送配置
  push: {
    enabled: true,
    channels: {
      wechat: true,
      sms: false,
      email: false,
    },
  },
};
