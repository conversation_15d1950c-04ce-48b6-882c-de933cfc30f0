# 评价数据分析接口测试

## 接口列表

### 1. 评价列表查询接口
```
GET /reviews?current=1&pageSize=20&keyword=测试&startDate=2024-01-01&endDate=2024-12-31&rating=5&employeeId=1
```

### 2. 评价统计概览接口
```
GET /reviews/statistics?startDate=2024-01-01&endDate=2024-12-31
```

### 3. 评价趋势数据接口
```
GET /reviews/trend?startDate=2024-01-01&endDate=2024-12-31
```

### 4. 评分分布统计接口
```
GET /reviews/rating-distribution?startDate=2024-01-01&endDate=2024-12-31
```

### 5. 员工评分排行接口
```
GET /reviews/employee-ranking?limit=10&startDate=2024-01-01&endDate=2024-12-31
```

## 返回数据格式

### 评价列表
```json
{
  "errCode": 0,
  "data": {
    "total": 100,
    "list": [
      {
        "id": 1,
        "orderId": 123,
        "rating": 5,
        "comment": "服务很好",
        "photoURLs": ["url1", "url2"],
        "createdAt": "2024-01-01T10:00:00.000Z",
        "order": {
          "sn": "ORD20240101001",
          "employee": {
            "id": 1,
            "name": "张三",
            "phone": "13800138000",
            "level": 3,
            "rating": 4.8,
            "walletBalance": 1000.00,
            "status": 1
          }
        },
        "customer": {
          "id": 1,
          "nickname": "客户昵称",
          "phone": "13900139000",
          "memberStatus": 1,
          "points": 100,
          "status": 1
        }
      }
    ]
  }
}
```

### 统计概览
```json
{
  "errCode": 0,
  "data": {
    "totalReviews": 1000,
    "todayReviews": 10,
    "averageRating": 4.5,
    "positiveRate": 85.5,
    "thisWeekReviews": 50,
    "thisMonthReviews": 200
  }
}
```

### 趋势数据
```json
{
  "errCode": 0,
  "data": [
    {
      "date": "2024-01-01",
      "reviews": 10,
      "averageRating": 4.5
    }
  ]
}
```

### 评分分布
```json
{
  "errCode": 0,
  "data": [
    {
      "rating": 5,
      "count": 500,
      "percentage": 50.0
    },
    {
      "rating": 4,
      "count": 300,
      "percentage": 30.0
    }
  ]
}
```

### 员工排行
```json
{
  "errCode": 0,
  "data": [
    {
      "employeeId": 1,
      "employeeName": "张三",
      "averageRating": 4.8,
      "reviewCount": 100,
      "level": 3
    }
  ]
}
```
