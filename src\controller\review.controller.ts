import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ReviewService } from '../service/review.service';
import { Op } from 'sequelize';
import { Order } from '../entity/order.entity';
import { Customer } from '../entity/customer.entity';
import { Employee } from '../entity/employee.entity';
import {
  CreateReviewDto,
  UpdateReviewDto,
  QueryReviewDto,
  ReviewTrendDto,
  ReviewStatisticsDto,
  EmployeeRankingDto,
} from '../dto/review.dto';
import { CustomError } from '../error/custom.error';

@Controller('/reviews')
export class ReviewController {
  @Inject()
  ctx: Context;

  @Inject()
  service: ReviewService;

  @Get('/', { summary: '查询评价列表' })
  async index(@Query() query: QueryReviewDto) {
    let { offset, limit } = query;
    const {
      offset: o,
      limit: l,
      current,
      pageSize,
      keyword,
      startDate,
      endDate,
      employeeId,
      filter,
      ...queryInfo
    } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }

    // 构建查询条件
    const whereCondition: any = { ...queryInfo };

    // 处理日期范围查询
    if (startDate || endDate) {
      whereCondition.createdAt = {};
      if (startDate) {
        whereCondition.createdAt[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereCondition.createdAt[Op.lte] = new Date(endDate + ' 23:59:59');
      }
    }

    // 处理关键词搜索评价内容
    if (keyword) {
      whereCondition[Op.or] = [{ comment: { [Op.like]: `%${keyword}%` } }];
    }

    // 处理关联查询
    const includeOptions: any[] = [
      {
        model: Order,
        as: 'order',
        attributes: ['id', 'sn'],
        include: [
          {
            model: Employee,
            as: 'employee',
            attributes: [
              'id',
              'name',
              'phone',
              'level',
              'rating',
              'walletBalance',
              'status',
            ],
            where: employeeId ? { id: employeeId } : undefined,
            required: !!employeeId,
          },
        ],
      },
      {
        model: Customer,
        as: 'customer',
        attributes: [
          'id',
          'nickname',
          'phone',
          'memberStatus',
          'points',
          'status',
        ],
        where: keyword
          ? {
              [Op.or]: [
                { nickname: { [Op.like]: `%${keyword}%` } },
                { phone: { [Op.like]: `%${keyword}%` } },
              ],
            }
          : undefined,
        required: !!keyword,
      },
    ];

    return this.service.findAll({
      query: whereCondition,
      offset,
      limit,
      filter,
      order: [['createdAt', 'DESC']],
      include: includeOptions,
    });
  }

  @Get('/:id', { summary: '按ID查询评价' })
  async show(@Param('id') id: number) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定评价');
    }
    return res;
  }

  @Post('/', { summary: '创建评价' })
  async create(@Body() createDto: CreateReviewDto & { customerId: number }) {
    const { orderId, rating, comment, photoURLs, customerId } = createDto;

    if (!customerId) {
      throw new CustomError('客户ID不能为空', 400);
    }

    return await this.service.createReview(
      customerId,
      orderId,
      rating,
      comment,
      photoURLs
    );
  }

  @Put('/:id', { summary: '更新评价' })
  async update(
    @Param('id') id: number,
    @Body() updateDto: UpdateReviewDto & { customerId: number }
  ) {
    const { customerId, ...updateData } = updateDto;
    if (!customerId) {
      throw new CustomError('客户ID不能为空', 400);
    }

    return await this.service.updateReview(customerId, id, updateData);
  }

  @Del('/:id', { summary: '删除评价' })
  async delete(@Param('id') id: number, @Body() body: { customerId: number }) {
    const { customerId } = body;
    if (!customerId) {
      throw new CustomError('客户ID不能为空', 400);
    }

    return await this.service.deleteReview(customerId, id);
  }

  @Get('/order/:orderId', { summary: '根据订单ID获取评价' })
  async getByOrderId(@Param('orderId') orderId: number) {
    const review = await this.service.getReviewByOrderId(orderId);
    if (!review) {
      throw new CustomError('该订单暂无评价', 404);
    }
    return review;
  }

  @Get('/customer/:customerId', { summary: '获取客户评价列表' })
  async getCustomerReviews(
    @Param('customerId') customerId: number,
    @Query('page') page = 1,
    @Query('pageSize') pageSize = 10
  ) {
    return await this.service.getCustomerReviews(customerId, page, pageSize);
  }

  @Get('/service/:serviceId', { summary: '获取服务评价列表' })
  async getServiceReviews(
    @Param('serviceId') serviceId: number,
    @Query('page') page = 1,
    @Query('pageSize') pageSize = 10
  ) {
    return await this.service.getServiceReviews(serviceId, page, pageSize);
  }

  @Get('/statistics', { summary: '获取评价统计概览' })
  async getStatistics(@Query() query: ReviewStatisticsDto) {
    const { startDate, endDate } = query;
    return await this.service.getReviewStatistics(startDate, endDate);
  }

  @Get('/trend', { summary: '获取评价趋势数据' })
  async getTrend(@Query() query: ReviewTrendDto) {
    const { startDate, endDate } = query;
    return await this.service.getReviewTrend(startDate, endDate);
  }

  @Get('/rating-distribution', { summary: '获取评分分布统计' })
  async getRatingDistribution(@Query() query: ReviewStatisticsDto) {
    const { startDate, endDate } = query;
    return await this.service.getRatingDistribution(startDate, endDate);
  }

  @Get('/employee-ranking', { summary: '获取员工评分排行' })
  async getEmployeeRanking(@Query() query: EmployeeRankingDto) {
    const { limit = 10, startDate, endDate } = query;
    return await this.service.getEmployeeRanking(limit, startDate, endDate);
  }
}
