import { createApp, close, createHttpRequest } from '@midwayjs/mock';
import { Framework } from '@midwayjs/koa';

describe('test/review-analysis.test.ts', () => {
  let app: any;

  beforeAll(async () => {
    try {
      app = await createApp<Framework>();
    } catch (err) {
      console.error('setup error', err);
      throw err;
    }
  });

  afterAll(async () => {
    await close(app);
  });

  it('should GET /reviews/statistics', async () => {
    const result = await createHttpRequest(app)
      .get('/reviews/statistics')
      .expect(200);

    expect(result.body.errCode).toBe(0);
    expect(result.body.data).toHaveProperty('totalReviews');
    expect(result.body.data).toHaveProperty('todayReviews');
    expect(result.body.data).toHaveProperty('averageRating');
    expect(result.body.data).toHaveProperty('positiveRate');
    expect(result.body.data).toHaveProperty('thisWeekReviews');
    expect(result.body.data).toHaveProperty('thisMonthReviews');
  });

  it('should GET /reviews/trend', async () => {
    const result = await createHttpRequest(app)
      .get('/reviews/trend')
      .query({
        startDate: '2024-01-01',
        endDate: '2024-12-31'
      })
      .expect(200);

    expect(result.body.errCode).toBe(0);
    expect(Array.isArray(result.body.data)).toBe(true);
  });

  it('should GET /reviews/rating-distribution', async () => {
    const result = await createHttpRequest(app)
      .get('/reviews/rating-distribution')
      .expect(200);

    expect(result.body.errCode).toBe(0);
    expect(Array.isArray(result.body.data)).toBe(true);
  });

  it('should GET /reviews/employee-ranking', async () => {
    const result = await createHttpRequest(app)
      .get('/reviews/employee-ranking')
      .query({
        limit: 10
      })
      .expect(200);

    expect(result.body.errCode).toBe(0);
    expect(Array.isArray(result.body.data)).toBe(true);
  });

  it('should GET /reviews with enhanced query', async () => {
    const result = await createHttpRequest(app)
      .get('/reviews')
      .query({
        current: 1,
        pageSize: 20,
        keyword: '测试',
        startDate: '2024-01-01',
        endDate: '2024-12-31',
        rating: 5
      })
      .expect(200);

    expect(result.body.errCode).toBe(0);
    expect(result.body.data).toHaveProperty('list');
    expect(result.body.data).toHaveProperty('total');
    expect(Array.isArray(result.body.data.list)).toBe(true);
  });
});
